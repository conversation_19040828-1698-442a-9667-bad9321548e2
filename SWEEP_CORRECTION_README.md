# 扫频校正功能说明

## 功能概述

本功能实现了在扫频后按第八个按钮时，对信号处理进行扫频校正的功能：

1. **10kHz以下信号**：FFT后的幅度谱乘以扫频得到的电压幅度比，然后进行IFFT-DAC输出
2. **10kHz以上正弦波**：输出幅度直接乘以扫频得到的电压幅度比

## 实现原理

### 扫频数据收集
- 在第二次扫频（Phase 2）过程中，系统会自动收集1kHz-400kHz范围内的电压幅度比数据
- 数据经过平滑滤波处理，提高准确性
- 扫频完成后，数据自动保存到校正数组中

### 校正应用
1. **FFT校正**（<10kHz）：
   - 在ADC3_ProcessFFT函数中，FFT分析完成后应用校正
   - 只对10kHz以下的频率分量进行校正
   - 校正公式：`corrected_amplitude = original_amplitude × sweep_ratio`

2. **正弦波校正**（≥10kHz）：
   - 在DAC_GetAmplitudeForFrequency函数中应用校正
   - 对10kHz以上的频率输出幅度进行校正
   - 校正公式：`corrected_peak_to_peak = original_peak_to_peak × sweep_ratio`

## 使用流程

1. **运行扫频测试**：
   - 按PE2键或选择第7个按钮启动扫频测试
   - 等待三个阶段完成：低频检测 → 完整扫频 → 归一化处理
   - 扫频完成后，系统会显示"Phase 2 sweep data saved"

2. **使用第八个按钮**：
   - 扫频完成后，按PA0键或选择第8个按钮
   - 系统会检查是否有扫频数据，如果没有会提示"Please run sweep first!"
   - 有数据时会启动ADC3处理，并自动应用扫频校正

## 关键变量

- `sweep_phase2_data[]`: 存储第二次扫频的完整数据
- `sweep_phase2_count`: 第二次扫频数据点数
- `sweep_phase2_completed`: 第二次扫频是否完成的标志
- `sweep_correction_data[]`: 用于校正的数据数组
- `use_sweep_correction`: 是否启用扫频校正的标志

## 关键函数

- `SaveSweepPhase2Data()`: 保存第二次扫频数据
- `GetSweepPhase2Ratio()`: 获取指定频率的电压比
- `LoadSweepPhase2ToCorrection()`: 将扫频数据加载到校正数组
- `ADC3_ApplySweepCorrection()`: 对FFT结果应用校正
- `DAC_GetAmplitudeForFrequency()`: 对DAC输出幅度应用校正

## 调试信息

系统会输出详细的调试信息：
- 扫频数据保存状态
- 关键频率点的校正值
- FFT校正应用情况
- DAC幅度校正情况

## 内存优化

为了适应STM32F4的内存限制，进行了以下优化：

- 扫频校正数据数组：50个点（600字节）
- 第二次扫频数据：30个关键点（360字节）
- ADC采样缓冲区：从1024减少到512点
- DAC正弦波查找表：从256减少到128点
- 扫频结果缓冲区：从50减少到20点
- 平滑滤波器：从5点减少到3点

## 数据采集策略

由于内存限制，第二次扫频数据采用智能采集策略：
- 每50个扫频点保存1个
- 关键频率点（1k, 5k, 10k, 20k, 50k, 100kHz附近）强制保存
- 确保重要频率点的校正精度

## 注意事项

1. 必须先完成扫频测试才能使用第八个按钮的校正功能
2. 校正数据基于第二次扫频的结果，使用不归一化的电压幅度比
3. 10kHz是分界点：低于此频率校正FFT，高于此频率校正正弦波输出
4. 校正会选择最接近的频率点，不进行插值计算
5. 由于内存优化，校正精度在关键频率点最高
