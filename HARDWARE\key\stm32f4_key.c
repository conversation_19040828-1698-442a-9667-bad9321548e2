#include "stm32f4_key.h"
void key_config(void)
{ 
		GPIO_InitTypeDef GPIO_CHU;
			
		RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOE,ENABLE);//shi neng E
		RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA,ENABLE);//shi neng A
        RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOC,ENABLE);//shi neng C
        RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOD,ENABLE);//shi neng D

		//E2 - 新增PE2按键用于扫频测试
		GPIO_CHU.GPIO_Pin=GPIO_Pin_2;
		GPIO_CHU.GPIO_Mode=GPIO_Mode_IN;//shu ru
		GPIO_CHU.GPIO_OType=GPIO_OType_PP;//tiu wan
		GPIO_CHU.GPIO_PuPd=GPIO_PuPd_UP;//shang la
		GPIO_CHU.GPIO_Speed=GPIO_Speed_50MHz;

		GPIO_Init(GPIOE,&GPIO_CHU);
		GPIO_SetBits(GPIOE,GPIO_Pin_2);

		//E3
		GPIO_CHU.GPIO_Pin=GPIO_Pin_3;
		GPIO_CHU.GPIO_Mode=GPIO_Mode_IN;//shu ru
		GPIO_CHU.GPIO_OType=GPIO_OType_PP;//tiu wan
		GPIO_CHU.GPIO_PuPd=GPIO_PuPd_UP;//shang la
		GPIO_CHU.GPIO_Speed=GPIO_Speed_50MHz;

		GPIO_Init(GPIOE,&GPIO_CHU);
		GPIO_SetBits(GPIOE,GPIO_Pin_3);

		//E4
		GPIO_CHU.GPIO_Pin=GPIO_Pin_4;
		GPIO_CHU.GPIO_Mode=GPIO_Mode_IN;//shu ru
		GPIO_CHU.GPIO_OType=GPIO_OType_PP;//tiu wan
		GPIO_CHU.GPIO_PuPd=GPIO_PuPd_UP;//shang la
		GPIO_CHU.GPIO_Speed=GPIO_Speed_50MHz;

		GPIO_Init(GPIOE,&GPIO_CHU);
		GPIO_SetBits(GPIOE,GPIO_Pin_4);
       
	    //E9
        GPIO_CHU.GPIO_Pin=GPIO_Pin_9;
		GPIO_CHU.GPIO_Mode=GPIO_Mode_OUT;//shu ru
		GPIO_CHU.GPIO_OType=GPIO_OType_PP;//tiu wan
		GPIO_CHU.GPIO_PuPd=GPIO_PuPd_DOWN;//下拉
		GPIO_CHU.GPIO_Speed=GPIO_Speed_50MHz;
		
		GPIO_Init(GPIOE,&GPIO_CHU);
		GPIO_SetBits(GPIOE,GPIO_Pin_9);	

        //E11
        GPIO_CHU.GPIO_Pin=GPIO_Pin_11;
		GPIO_CHU.GPIO_Mode=GPIO_Mode_OUT;//shu ru
		GPIO_CHU.GPIO_OType=GPIO_OType_PP;//tiu wan
		GPIO_CHU.GPIO_PuPd=GPIO_PuPd_DOWN;//下拉
		GPIO_CHU.GPIO_Speed=GPIO_Speed_50MHz;
		
		GPIO_Init(GPIOE,&GPIO_CHU);
		GPIO_SetBits(GPIOE,GPIO_Pin_11);	

        //E13
        GPIO_CHU.GPIO_Pin=GPIO_Pin_13;
		GPIO_CHU.GPIO_Mode=GPIO_Mode_OUT;//shu ru
		GPIO_CHU.GPIO_OType=GPIO_OType_PP;//tiu wan
		GPIO_CHU.GPIO_PuPd=GPIO_PuPd_DOWN;//下拉
		GPIO_CHU.GPIO_Speed=GPIO_Speed_50MHz;
		
		GPIO_Init(GPIOE,&GPIO_CHU);
		GPIO_SetBits(GPIOE,GPIO_Pin_13);

        //A0
        GPIO_CHU.GPIO_Pin=GPIO_Pin_0;
		GPIO_CHU.GPIO_Mode=GPIO_Mode_IN;//shu ru
		GPIO_CHU.GPIO_OType=GPIO_OType_PP;//tiu wan
		GPIO_CHU.GPIO_PuPd=GPIO_PuPd_DOWN;//xia la
		GPIO_CHU.GPIO_Speed=GPIO_Speed_50MHz;
		
		GPIO_Init(GPIOA,&GPIO_CHU);
		GPIO_SetBits(GPIOA,GPIO_Pin_0);

//        //A13 1
//        GPIO_CHU.GPIO_Pin=GPIO_Pin_13;
//		GPIO_CHU.GPIO_Mode=GPIO_Mode_OUT;//shu ru
//		GPIO_CHU.GPIO_OType=GPIO_OType_PP;//tiu wan
//		GPIO_CHU.GPIO_PuPd=GPIO_PuPd_DOWN;//xia la
//		GPIO_CHU.GPIO_Speed=GPIO_Speed_50MHz;
//		
//		GPIO_Init(GPIOA,&GPIO_CHU);
//		GPIO_SetBits(GPIOA,GPIO_Pin_13);

		//A15 1
		GPIO_CHU.GPIO_Pin=GPIO_Pin_15;
		GPIO_CHU.GPIO_Mode=GPIO_Mode_IN;//shu ru
		GPIO_CHU.GPIO_OType=GPIO_OType_PP;//tiu wan
		GPIO_CHU.GPIO_PuPd=GPIO_PuPd_DOWN;//xia la
		GPIO_CHU.GPIO_Speed=GPIO_Speed_50MHz;
		
		GPIO_Init(GPIOA,&GPIO_CHU);
		GPIO_SetBits(GPIOA,GPIO_Pin_15);

		//C11 2
        GPIO_CHU.GPIO_Pin=GPIO_Pin_11;
		GPIO_CHU.GPIO_Mode=GPIO_Mode_IN;//shu ru
		GPIO_CHU.GPIO_OType=GPIO_OType_PP;//tiu wan
		GPIO_CHU.GPIO_PuPd=GPIO_PuPd_DOWN;//xia la
		GPIO_CHU.GPIO_Speed=GPIO_Speed_50MHz;
		
		GPIO_Init(GPIOC,&GPIO_CHU);
		GPIO_SetBits(GPIOC,GPIO_Pin_11);

        //D0 3
        GPIO_CHU.GPIO_Pin=GPIO_Pin_0;
		GPIO_CHU.GPIO_Mode=GPIO_Mode_IN;//shu ru
		GPIO_CHU.GPIO_OType=GPIO_OType_PP;//tiu wan
		GPIO_CHU.GPIO_PuPd=GPIO_PuPd_DOWN;//xia la
		GPIO_CHU.GPIO_Speed=GPIO_Speed_50MHz;
		
		GPIO_Init(GPIOD,&GPIO_CHU);
		GPIO_SetBits(GPIOD,GPIO_Pin_0);

		//D2 4
        GPIO_CHU.GPIO_Pin=GPIO_Pin_2;
		GPIO_CHU.GPIO_Mode=GPIO_Mode_IN;//shu ru
		GPIO_CHU.GPIO_OType=GPIO_OType_PP;//tiu wan
		GPIO_CHU.GPIO_PuPd=GPIO_PuPd_DOWN;//xia la
		GPIO_CHU.GPIO_Speed=GPIO_Speed_50MHz;
		
		GPIO_Init(GPIOD,&GPIO_CHU);
		GPIO_SetBits(GPIOD,GPIO_Pin_2);
        
        //D4 5
        GPIO_CHU.GPIO_Pin=GPIO_Pin_4;
		GPIO_CHU.GPIO_Mode=GPIO_Mode_IN;//shu ru
		GPIO_CHU.GPIO_OType=GPIO_OType_PP;//tiu wan
		GPIO_CHU.GPIO_PuPd=GPIO_PuPd_DOWN;//xia la
		GPIO_CHU.GPIO_Speed=GPIO_Speed_50MHz;
		
		GPIO_Init(GPIOD,&GPIO_CHU);
		GPIO_SetBits(GPIOD,GPIO_Pin_4);

//		//D6 1
//        GPIO_CHU.GPIO_Pin=GPIO_Pin_6;
//		GPIO_CHU.GPIO_Mode=GPIO_Mode_IN;//shu ru
//		GPIO_CHU.GPIO_OType=GPIO_OType_PP;//tiu wan
//		GPIO_CHU.GPIO_PuPd=GPIO_PuPd_DOWN;//xia la
//		GPIO_CHU.GPIO_Speed=GPIO_Speed_50MHz;
//		
//		GPIO_Init(GPIOD,&GPIO_CHU);
//		GPIO_SetBits(GPIOD,GPIO_Pin_6);
     
}    
 
