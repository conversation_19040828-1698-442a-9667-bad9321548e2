#ifndef _TIMER_H
#define _TIMER_H
#include "sys.h"
//////////////////////////////////////////////////////////////////////////////////	 
//������ֻ��ѧϰʹ�ã�δ���������ɣ��������������κ���;
//ALIENTEK STM32F407������
//��ʱ�� ��������	   
//����ԭ��@ALIENTEK
//������̳:www.openedv.com
//��������:2014/6/16
//�汾��V1.0
//��Ȩ���У�����ؾ���
//Copyright(C) �������������ӿƼ����޹�˾ 2014-2024
//All rights reserved									  
////////////////////////////////////////////////////////////////////////////////// 	

void TIM3_Int_Init(u16 arr,u16 psc);
void TIM4_Int_Init(u16 arr1,u16 psc1);
void TIM5_ADC3_Init(u16 arr, u16 psc);  // TIM5专用于ADC3采样
void TIM1_PWM_Init(u32 arr2,u32 ccr2);
void TIM6_DAC_Init(u16 arr, u16 psc);  // TIM6用于DAC正弦波输出
#endif
