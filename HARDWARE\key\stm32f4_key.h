#ifndef __STM32F4_KEY_H
#define __STM32F4_KEY_H

#include "stm32f4xx.h"

#include "sys.h"

#define KEY0 PEin(4)//PE4
#define KEY1 PEin(3)//PE3
#define KEY2 PEin(2)//PE2 - 扫频测试按键
#define WK   PAin(0)//PA0 - ADC3处理按键
#define A1   PEout(9)
#define B1   PEout(11)
#define C1   PEout(13)

#define F4   PAin(15)
#define F3   PCin(11)
#define F2   PDin(0)
#define F1   PDin(2)
#define F0   PDin(4)


void key_config(void);
#endif
